/**
 * Test file for CSRC Fund API
 * Run with: node src/utils/csrc-fund-api.test.js
 */

const {
  fetchFundData,
  searchFundsByName,
  getFundByCode,
  getAllFunds,
  getFundsByCompany
} = require('./csrc-fund-api');

async function runTests() {
  console.log('=== CSRC Fund API Comprehensive Tests ===\n');

  try {
    // Test 1: Search funds by name
    console.log('1. Searching funds by name (沪深300):');
    const funds300 = await searchFundsByName('沪深300', 5);
    console.log(`Found ${funds300.length} funds:`);
    funds300.forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.name}`);
      console.log(`     Net Value: ${fund.shareNetValue}, Date: ${fund.valuationDate}`);
    });
    console.log();

    // Test 2: Get specific fund by code
    console.log('2. Getting specific fund by code (110003):');
    const specificFund = await getFundByCode('110003');
    if (specificFund) {
      console.log(`  Code: ${specificFund.code}`);
      console.log(`  Name: ${specificFund.name}`);
      console.log(`  Share Net Value: ${specificFund.shareNetValue}`);
      console.log(`  Total Net Value: ${specificFund.totalNetValue}`);
      console.log(`  Valuation Date: ${specificFund.valuationDate}`);
    } else {
      console.log('  Fund not found');
    }
    console.log();

    // Test 3: Get all funds with pagination
    console.log('3. Getting all funds (first page, 10 records):');
    const allFunds = await getAllFunds(0, 10);
    console.log(`  Total records: ${allFunds.totalRecords}`);
    console.log(`  Total pages: ${allFunds.totalPages}`);
    console.log(`  Current page: ${allFunds.currentPage + 1}`);
    console.log('  Sample funds:');
    allFunds.funds.slice(0, 5).forEach((fund, index) => {
      console.log(`    ${index + 1}. ${fund.code} - ${fund.name}`);
    });
    console.log();

    // Test 4: Search by fund company
    console.log('4. Getting funds by company (华夏):');
    const companyFunds = await getFundsByCompany('华夏', 5);
    console.log(`Found ${companyFunds.length} funds from 华夏:`);
    companyFunds.forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.name}`);
    });
    console.log();

    // Test 5: Search for index funds
    console.log('5. Searching for index funds (指数):');
    const indexFunds = await searchFundsByName('指数', 5);
    console.log(`Found ${indexFunds.length} index funds:`);
    indexFunds.forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.name}`);
    });
    console.log();

    // Test 6: Search for ETF funds
    console.log('6. Searching for ETF funds:');
    const etfFunds = await searchFundsByName('ETF', 5);
    console.log(`Found ${etfFunds.length} ETF funds:`);
    etfFunds.forEach((fund, index) => {
      console.log(`  ${index + 1}. ${fund.code} - ${fund.name}`);
    });
    console.log();

    // Test 7: Raw API call with custom parameters
    console.log('7. Raw API call with custom parameters:');
    const rawData = await fetchFundData({
      fundName: '债券',
      iDisplayLength: 3,
      startDate: '2024-12-31',
      endDate: '2024-12-31'
    });
    console.log(`  Total records: ${rawData.iTotalRecords}`);
    console.log(`  Returned records: ${rawData.aaData.length}`);
    rawData.aaData.forEach((fund, index) => {
      console.log(`    ${index + 1}. ${fund.code} - ${fund.shortName}`);
      console.log(`       Date: ${fund.valuationDate}, Value: ${fund.shareNetValue}`);
    });
    console.log();

    // Test 8: Search for popular fund types
    console.log('8. Searching for different fund types:');
    
    const fundTypes = ['混合', '股票', '债券', '货币'];
    for (const type of fundTypes) {
      try {
        const typeFunds = await searchFundsByName(type, 2);
        console.log(`  ${type} funds (${typeFunds.length} found):`);
        typeFunds.forEach((fund, index) => {
          console.log(`    ${index + 1}. ${fund.code} - ${fund.name}`);
        });
      } catch (error) {
        console.log(`  Error searching for ${type} funds: ${error.message}`);
      }
    }
    console.log();

    console.log('=== All tests completed successfully! ===');

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Helper function to demonstrate API parameters
function demonstrateAPIParameters() {
  console.log('\n=== API Parameters Documentation ===');
  console.log('The CSRC Fund API accepts the following parameters:');
  console.log('');
  console.log('Pagination:');
  console.log('  - iDisplayStart: Starting record index (0-based)');
  console.log('  - iDisplayLength: Number of records to return (max ~100)');
  console.log('');
  console.log('Search filters:');
  console.log('  - fundCode: Exact fund code (e.g., "110003")');
  console.log('  - fundName: Partial fund name (e.g., "沪深300", "ETF")');
  console.log('  - fundCompanyShortName: Fund company name (e.g., "华夏")');
  console.log('  - fundType: Fund type ("all" or specific type)');
  console.log('');
  console.log('Date range:');
  console.log('  - startDate: Start date (YYYY-MM-DD format)');
  console.log('  - endDate: End date (YYYY-MM-DD format)');
  console.log('');
  console.log('Response format:');
  console.log('  - iTotalRecords: Total number of matching records');
  console.log('  - aaData: Array of fund objects');
  console.log('');
  console.log('Fund object properties:');
  console.log('  - code: Fund code');
  console.log('  - shortName: Fund name');
  console.log('  - shareNetValue: Share net value');
  console.log('  - totalNetValue: Total net value');
  console.log('  - valuationDate: Valuation date');
  console.log('  - classification: Fund classification info');
  console.log('  - fund: Additional fund metadata');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests()
    .then(() => {
      demonstrateAPIParameters();
    })
    .catch(console.error);
}

module.exports = { runTests, demonstrateAPIParameters };
